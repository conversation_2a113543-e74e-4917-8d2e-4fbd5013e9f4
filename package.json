{"name": "linkding", "version": "1.42.0", "description": "", "main": "index.js", "scripts": {"build": "npm run build-js && npm run build-theme-light && npm run build-theme-dark", "build-js": "rollup -c", "build-theme-light": "postcss -o bookmarks/static/theme-light.css bookmarks/styles/theme-light.css", "build-theme-dark": "postcss -o bookmarks/static/theme-dark.css bookmarks/styles/theme-dark.css", "dev": "rollup -c -w"}, "repository": {"type": "git", "url": "git+https://github.com/sissbruecker/linkding.git"}, "keywords": [], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/sissbruecker/linkding/issues"}, "homepage": "https://github.com/sissbruecker/linkding#readme", "dependencies": {"@hotwired/turbo": "^8.0.6", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/wasm-node": "^4.13.0", "cssnano": "^7.0.6", "postcss": "^8.4.45", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.0", "rollup-plugin-svelte": "^7.2.0", "svelte": "^4.0.0"}, "devDependencies": {"prettier": "^3.3.3"}, "web-types": "./web-types.json"}