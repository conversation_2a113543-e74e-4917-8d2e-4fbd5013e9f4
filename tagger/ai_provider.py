from typing import Protocol


class AIProvider(Protocol):
    """Protocol for AI model provider classes."""

    def __init__(self, model: str) -> None:
        """Initialize the AI provider with a specific model.

        Args:
            model: The name/identifier of the AI model to use.
        """
        ...

    def generate(self, prompt: str, output_schema: dict) -> str:
        """Generate response using AI model with structured output.

        Args:
            prompt: The input prompt to generate a response for.
            output_schema: Schema defining the expected output format.

        Returns:
            The generated response as a string.
        """
        ...
