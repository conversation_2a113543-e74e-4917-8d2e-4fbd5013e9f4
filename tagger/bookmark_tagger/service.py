import os
from functools import cache

from jinja2 import Environment, FileSystemLoader

from tagger.ai_provider import AIProvider
from tagger.bookmark_tagger.domain import Bookmark, FileContent, TaggedBookmark


@cache
def read_vocabulary_file() -> dict[str, str]:
    """Reads vocabulary file and returns the content as a string."""
    vocabulary = ""
    with open("tags.md", "r") as file:
        vocabulary = [line for line in file.readlines() if not line.startswith("#")]
        vocabulary = "".join(vocabulary).strip()
    return {"content": vocabulary}


def prompt_from_template(bookmark: Bookmark, vocabulary_file: FileContent) -> str:
    """Creates a prompt in XML format using Jinja2 template for bookmark categorization."""
    env = Environment(loader=FileSystemLoader(os.path.dirname(__file__)))
    template = env.get_template("prompt_template.xml.jinja")
    return template.render(
        vocabulary=vocabulary_file.content,
        bookmark=bookmark,
    )


def generate_bookmark_tags(
    bookmark: Bookmark,
    ai_provider: AIProvider,
) -> list[str]:
    vocabulary_file = read_vocabulary_file()
    file_content = FileContent.model_validate(vocabulary_file)
    prompt = prompt_from_template(bookmark, file_content)
    print(prompt)

    response = ai_provider.generate(
        prompt=prompt,
        output_schema=TaggedBookmark.model_json_schema(),
    )
    if not response:
        return []
    return TaggedBookmark.model_validate_json(response).tags
