from infrastructure.ollama import OllamaProvider
from infrastructure.playwright import PlaywrightProvider
from tagger.bookmark_tagger.domain import Bookmark
from tagger.bookmark_tagger.service import generate_bookmark_tags
from tagger.site_summariser.domain import SiteContent
from tagger.site_summariser.service import site_summariser


def main():
    bookmark = Bookmark(
        url="https://polotek.net/posts/the-frontend-treadmill/",
        title="The Frontend Treadmill - These Yaks Ain’t Gonna Shave Themselves",
        description="""A lot of frontend teams are very convinced that rewriting their frontend will lead to the promised land. And I am the bearer of bad tidings.
If you are building a product that you hope has longevity, your frontend framework is the least interesting technical decision for you to make. And all of the time you spend arguing about it is wasted energy.
I will die on this hill.""",
        website_summary=None,
    )
    site_summariser_ai_provider = OllamaProvider("gemma3:12b")
    browser_provider = PlaywrightProvider()
    content = browser_provider.extract_text(bookmark.url)
    site_content = SiteContent.model_validate(content)
    summary = site_summariser(site_content, site_summariser_ai_provider)
    print(f"{summary=}")
    bookmark.website_summary = summary
    ai_provider = OllamaProvider()
    generated_tags = generate_bookmark_tags(bookmark, ai_provider)
    print(f"{bookmark.title=}")
    print(f"{generated_tags=}")


if __name__ == "__main__":
    main()
