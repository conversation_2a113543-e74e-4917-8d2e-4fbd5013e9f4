from infrastructure.ollama import OllamaProvider
from infrastructure.playwright import <PERSON><PERSON><PERSON><PERSON>ider
from tagger.site_summariser.domain import SiteContent
from tagger.site_summariser.service import site_summariser


def others():
    #     get_site_text = playwright_proivder()
    #     content = get_site_text("https://grownandhealthy.com/product/foot-freedom/")
    #     prompt_0 = (
    #         "Here is the text content of a website can you summarise it into a few sentences emphasising key points: "
    #         + content
    #     )

    #     prompt_1 = f""""Task: Summarize the following website content in 2-3 concise paragraphs.

    # Instructions:
    # - Focus on the main points and key information
    # - Remove promotional language and unnecessary details
    # - Maintain factual accuracy
    # - Use clear, readable language
    # - Keep the summary between 100-200 words

    # Website content:
    # {content}

    # Summary:"""

    #     prompt_2 = (
    #         "Summarise the following website content into a few sentences emphasising key points: "
    #         + content
    #     )
    #     prompt_3 = f"""Summarize this text in 3 sentences. Focus only on the most important facts:

    # {content}

    # Main points:
    # 1.
    # 2.
    # 3.
    #     """

    #     client = ollama.Client()
    #     for i, prompt in enumerate([prompt_0, prompt_1, prompt_2, prompt_3]):
    #         response = client.generate("gemma3:1b", prompt)
    #         print(f"prompt {i}:")
    #         print("---------")
    #         print(response.response)
    #         print("\n\n")
    pass


def main():
    ai_provider = OllamaProvider("gemma3:12b")
    browser_provider = PlaywrightProvider()
    content = browser_provider.extract_text(
        "https://grownandhealthy.com/product/foot-freedom/"
    )
    site_content = SiteContent.model_validate(content)
    summary = site_summariser(site_content, ai_provider)
    print(f"{summary=}")


if __name__ == "__main__":
    main()
