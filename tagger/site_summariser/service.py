import os

from jinja2 import Environment, FileSystemLoader

from tagger.ai_provider import AIProvider
from tagger.site_summariser.domain import SiteContent, SiteSummary


def prompt_from_template(site_content: SiteContent) -> str:
    """Creates a prompt in XML format using Jinja2 template for site summarization."""
    env = Environment(loader=FileSystemLoader(os.path.dirname(__file__)))
    template = env.get_template("prompt_template.xml.jinja")
    return template.render(site_content=site_content)


def site_summariser(site_content: SiteContent, ai_provider: AIProvider) -> str:
    prompt = prompt_from_template(site_content)
    response = ai_provider.generate(
        prompt=prompt,
        output_schema=SiteSummary.model_json_schema(),
    )
    if not response:
        return ""
    return SiteSummary.model_validate_json(response).summery
