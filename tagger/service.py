import asyncio

from infrastructure import linkding
from infrastructure.ollama import OllamaProvider
from tagger.bookmark_tagger.domain import Bookmark
from tagger.bookmark_tagger.service import generate_bookmark_tags


async def async_tag_bookmark(id: int, model: str) -> None:
    bookmark = await linkding.get_bookmark(id)
    bookmark = Bookmark.model_validate(bookmark)
    ai_provider = OllamaProvider(model)
    bookmark_tags = generate_bookmark_tags(bookmark, ai_provider)
    print(f"{bookmark_tags}")


def tag_bookmark(id: int, model: str) -> None:
    asyncio.run(async_tag_bookmark(id, model))
