from typing import Protocol


class Browser<PERSON>rovider(Protocol):
    """Protocol for browser-based text extraction providers."""

    def __init__(self) -> None: ...

    def extract_text(self, url: str) -> dict[str, str]:
        """Extract text content from a web page.

        Args:
            url: The URL to extract text content from.

        Returns:
            A dictionary containing the extracted text content.
        """
        ...
