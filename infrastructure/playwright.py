from playwright.sync_api import sync_playwright

from tagger.browser_provider import BrowserProvider


class PlaywrightProvider(BrowserProvider):
    """Playwright implementation of BrowserProvider protocol."""

    def __init__(self):
        """Initialize the Playwright provider."""
        pass

    def extract_text(self, url: str) -> dict[str, str]:
        """Extract text content from a web page using Playwright.

        Args:
            url: The URL to extract text content from.

        Returns:
            A dictionary containing the extracted text content.
        """
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=True,
                args=[
                    "--no-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-gpu",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--memory-pressure-off",
                ],
            )

            page = browser.new_page()

            # Block unnecessary resources
            page.route(
                "**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}",
                lambda route: route.abort(),
            )

            try:
                page.goto(url, wait_until="domcontentloaded")
                return {"content": page.inner_text("body")}
            finally:
                browser.close()
