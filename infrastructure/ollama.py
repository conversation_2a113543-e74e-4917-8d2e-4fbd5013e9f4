from enum import StrEnum

import ollama

from tagger.ai_provider import AIProvider


class OllamaModel(StrEnum):
    gemma3 = "gemma3"
    gemma3_12b = "gemma3:12b"


class OllamaProvider(AIProvider):
    """Ollama implementation of AIProvider protocol."""

    def __init__(self, model: str = "gemma3:12b"):
        """Initialize the Ollama provider with a specific model.

        Args:
            model: The Ollama model to use for generation. Defaults to "gemma3:12b".
        """
        self.model = model

    def generate(self, prompt: str, output_schema: dict) -> str:
        """Generate response using Ollama with structured output.

        Args:
            prompt: The input prompt to generate a response for.
            output_schema: Schema defining the expected output format.

        Returns:
            The generated response as a string.
        """
        response = ollama.generate(
            prompt=prompt,
            model=self.model,
            format=output_schema,
        )
        return response.response or ""
