import asyncio
from contextlib import asynccontextmanager
from typing import Any

import aiohttp
from aiolinkding import async_get_client
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )
    linkding_token: str
    base_url: str


settings = Settings()  # pyright: ignore[reportCallIssue]


@asynccontextmanager
async def linkding_client_session():
    async with aiohttp.ClientSession() as session:
        client = await async_get_client(
            settings.base_url, settings.linkding_token, session=session
        )
        yield client


async def create_bookmarks(
    urls: list[str], dry_run=True
) -> dict[str, list[dict[str, Any]]]:
    if dry_run:
        for url in urls:
            print(url)

        return {"results": [{"url": url} for url in urls]}

    async with linkding_client_session() as client:
        coroutines = []
        for url in urls:
            coroutines.append(client.bookmarks.async_create(url))
        results = await asyncio.gather(*coroutines)
        return {"results": results}


async def get_bookmark(id: int) -> dict[str, Any]:
    async with linkding_client_session() as client:
        bookmark = await client.bookmarks.async_get_single(id)
        return bookmark


async def main():
    sites = []
    sites.append("https://monkeytype.com/account")
    sites.append("https://raytracing.github.io/books/RayTracingInOneWeekend.html")

    results = await create_bookmarks(sites, dry_run=False)
    assert len(results["results"]) == 2
    for bookmark in results["results"]:
        actual_url = bookmark["url"]
        assert actual_url in sites


if __name__ == "__main__":
    asyncio.run(main())
