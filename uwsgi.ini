[uwsgi]
module = bookmarks.wsgi:application
env = DJANGO_SETTINGS_MODULE=bookmarks.settings.prod
static-map = /static=static
static-map = /static=data/favicons
static-map = /static=data/previews
static-map = /robots.txt=static/robots.txt
processes = 2
threads = 2
pidfile = /tmp/linkding.pid
vacuum=True
stats = 127.0.0.1:9191
uid = www-data
gid = www-data
buffer-size = 8192
die-on-term = true

if-env = LD_CONTEXT_PATH
static-map = /%(_)static=static
static-map = /%(_)static=data/favicons
static-map = /%(_)static=data/previews
static-map = /%(_)robots.txt=static/robots.txt
endif =

if-env = LD_REQUEST_TIMEOUT
http-timeout = %(_)
socket-timeout = %(_)
harakiri = %(_)
endif =

if-env = LD_REQUEST_MAX_CONTENT_LENGTH
limit-post = %(_)
endif =

if-env = LD_LOG_X_FORWARDED_FOR
log-x-forwarded-for = %(_)
endif =

if-env = LD_DISABLE_REQUEST_LOGS=true
disable-logging = true
log-4xx = true
log-5xx = true
endif =
