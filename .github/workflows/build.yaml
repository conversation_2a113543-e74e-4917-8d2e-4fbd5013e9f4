name: build

on: workflow_dispatch

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Read version from file
        id: get_version
        run: echo "VERSION=$(cat version.txt)" >> $GITHUB_ENV

      - name: Login to <PERSON>er Hu<PERSON>
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ github.token }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build latest
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/default.Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          tags: |
            sissbruecker/linkding:latest
            sissbruecker/linkding:${{ env.VERSION }}
            ghcr.io/sissbruecker/linkding:latest
            ghcr.io/sissbruecker/linkding:${{ env.VERSION }}
          target: linkding
          push: true

      - name: Build latest-alpine
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/alpine.Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          tags: |
            sissbruecker/linkding:latest-alpine
            sissbruecker/linkding:${{ env.VERSION }}-alpine
            ghcr.io/sissbruecker/linkding:latest-alpine
            ghcr.io/sissbruecker/linkding:${{ env.VERSION }}-alpine
          target: linkding
          push: true

      - name: Build latest-plus
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/default.Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          tags: |
            sissbruecker/linkding:latest-plus
            sissbruecker/linkding:${{ env.VERSION }}-plus
            ghcr.io/sissbruecker/linkding:latest-plus
            ghcr.io/sissbruecker/linkding:${{ env.VERSION }}-plus
          target: linkding-plus
          push: true

      - name: Build latest-plus-alpine
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/alpine.Dockerfile
          platforms: linux/amd64,linux/arm64,linux/arm/v7
          tags: |
            sissbruecker/linkding:latest-plus-alpine
            sissbruecker/linkding:${{ env.VERSION }}-plus-alpine
            ghcr.io/sissbruecker/linkding:latest-plus-alpine
            ghcr.io/sissbruecker/linkding:${{ env.VERSION }}-plus-alpine
          target: linkding-plus
          push: true