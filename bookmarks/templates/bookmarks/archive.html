{% extends "bookmarks/layout.html" %}
{% load static %}
{% load shared %}
{% load bookmarks %}

{% block content %}
  <div ld-bulk-edit
       class="bookmarks-page grid columns-md-1 {% if bookmark_list.collapse_side_panel %}collapse-side-panel{% endif %}">

    {# Bookmark list #}
    <main class="main col-2" aria-labelledby="main-heading">
      <div class="section-header mb-0">
        <h1 id="main-heading">Archived bookmarks</h1>
        <div class="header-controls">
          {% bookmark_search bookmark_list.search mode='archived' %}
          {% include 'bookmarks/bulk_edit/toggle.html' %}
          <button ld-filter-drawer-trigger class="btn ml-2">Filters</button>
        </div>
      </div>

      <form class="bookmark-actions"
            action="{{ bookmark_list.action_url|safe }}"
            method="post" autocomplete="off">
        {% csrf_token %}
        {% include 'bookmarks/bulk_edit/bar.html' with disable_actions='bulk_archive' %}

        <div id="bookmark-list-container">
          {% include 'bookmarks/bookmark_list.html' %}
        </div>
      </form>
    </main>

    {# Filters #}
    <div class="side-panel col-1 hide-md">
      {% include 'bookmarks/bundle_section.html' %}
      {% include 'bookmarks/tag_section.html' %}
    </div>
  </div>
{% endblock %}

{% block overlays %}
  {# Bookmark details #}
  <turbo-frame id="details-modal" target="_top">
    {% if details %}
      {% include 'bookmarks/details/modal.html' %}
    {% endif %}
  </turbo-frame>
{% endblock %}
