{% extends "bookmarks/layout.html" %}
{% load static %}
{% load shared %}
{% load bookmarks %}

{% block content %}
  <div
      class="bookmarks-page grid columns-md-1 {% if bookmark_list.collapse_side_panel %}collapse-side-panel{% endif %}">

    {# Bookmark list #}
    <main class="main col-2" aria-labelledby="main-heading">
      <div class="section-header">
        <h1 id="main-heading">Shared bookmarks</h1>
        <div class="header-controls">
          {% bookmark_search bookmark_list.search mode='shared' %}
          <button ld-filter-drawer-trigger class="btn ml-2">Filters</button>
        </div>
      </div>

      <form class="bookmark-actions"
            action="{{ bookmark_list.action_url|safe }}"
            method="post" autocomplete="off">
        {% csrf_token %}
        <div id="bookmark-list-container">
          {% include 'bookmarks/bookmark_list.html' %}
        </div>
      </form>
    </main>

    {# Filters #}
    <div class="side-panel col-1 hide-md">
      <section aria-labelledby="user-heading">
        <div class="section-header">
          <h2 id="user-heading">User</h2>
        </div>
        <div>
          {% user_select bookmark_list.search users %}
          <br>
        </div>
      </section>
      {% include 'bookmarks/tag_section.html' %}
    </div>
  </div>
{% endblock %}

{% block overlays %}
  {# Bookmark details #}
  <turbo-frame id="details-modal" target="_top">
    {% if details %}
      {% include 'bookmarks/details/modal.html' %}
    {% endif %}
  </turbo-frame>
{% endblock %}
