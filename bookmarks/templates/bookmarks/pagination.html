{% load shared %}

<ul class="pagination">
  {% if prev_link %}
    <li class="page-item">
      <a href="{{ prev_link }}" tabindex="-1">Previous</a>
    </li>
  {% else %}
    <li class="page-item disabled">
      <a href="#" tabindex="-1">Previous</a>
    </li>
  {% endif %}

  {% for page_link in page_links %}
    {% if page_link %}
      <li class="page-item {% if page_link.active %}active{% endif %}">
        <a href="{{ page_link.link }}">{{ page_link.number }}</a>
      </li>
    {% else %}
      <li class="page-item">
        <span>...</span>
      </li>
    {% endif %}
  {% endfor %}

  {% if next_link %}
    <li class="page-item">
      <a href="{{ next_link }}" tabindex="-1">Next</a>
    </li>
  {% else %}
    <li class="page-item disabled">
      <a href="#" tabindex="-1">Next</a>
    </li>
  {% endif %}
</ul>