{% load shared %}
{% htmlmin %}
  <div class="tag-cloud">
    {% if tag_cloud.has_selected_tags %}
      <p class="selected-tags">
        {% for tag in tag_cloud.selected_tags %}
          <a href="?{% remove_tag_from_query tag.name %}"
             class="text-bold mr-2">
            <span>-{{ tag.name }}</span>
          </a>
        {% endfor %}
      </p>
    {% endif %}
    <div class="unselected-tags">
      {% for group in tag_cloud.groups %}
        <p class="group">
          {% for tag in group.tags %}
            {# Highlight first char of first tag in group #}
            {% if forloop.counter == 1 %}
              <a href="?{% add_tag_to_query tag.name %}"
                 class="mr-2" data-is-tag-item>
                <span
                    class="highlight-char">{{ tag.name|first_char }}</span><span>{{ tag.name|remaining_chars:1 }}</span>
              </a>
            {% else %}
              {# Render remaining tags normally #}
              <a href="?{% add_tag_to_query tag.name %}"
                 class="mr-2" data-is-tag-item>
                <span>{{ tag.name }}</span>
              </a>
            {% endif %}
          {% endfor %}
        </p>
      {% endfor %}
    </div>
  </div>
{% endhtmlmin %}
