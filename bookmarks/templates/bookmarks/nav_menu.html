{% load shared %}
{% htmlmin %}
  {# Basic menu list #}
  <div class="hide-md">
    <a href="{% url 'linkding:bookmarks.new' %}" class="btn btn-primary mr-2">Add bookmark</a>
    <div ld-dropdown class="dropdown">
      <button class="btn btn-link dropdown-toggle" tabindex="0">
        Bookmarks
      </button>
      <ul class="menu" role="list" tabindex="-1">
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}" class="menu-link">Active</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.archived' %}" class="menu-link">Archived</a>
        </li>
        {% if request.user_profile.enable_sharing %}
          <li class="menu-item">
            <a href="{% url 'linkding:bookmarks.shared' %}" class="menu-link">Shared</a>
          </li>
        {% endif %}
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}?unread=yes" class="menu-link">Unread</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}?q=!untagged" class="menu-link">Untagged</a>
        </li>
      </ul>
    </div>
    <div ld-dropdown class="dropdown">
      <button class="btn btn-link dropdown-toggle" tabindex="0">
        Settings
      </button>
      <ul class="menu" role="list" tabindex="-1">
        <li class="menu-item">
          <a href="{% url 'linkding:settings.general' %}" class="menu-link">General</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:settings.integrations' %}" class="menu-link">Integrations</a>
        </li>
        {% if request.user.is_superuser %}
          <li class="menu-item">
            <a href="{% url 'admin:index' %}" class="menu-link" data-turbo="false">Admin</a>
          </li>
        {% endif %}
      </ul>
    </div>
    <form class="d-inline" action="{% url 'logout' %}" method="post" data-turbo="false">
      {% csrf_token %}
      <button type="submit" class="btn btn-link">Logout</button>
    </form>
  </div>
  {# Menu drop-down for smaller devices #}
  <div class="show-md">
    <a href="{% url 'linkding:bookmarks.new' %}" aria-label="Add bookmark" class="btn btn-primary">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
           style="width: 24px; height: 24px">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
    </a>
    <div ld-dropdown class="dropdown dropdown-right">
      <button class="btn btn-link dropdown-toggle" aria-label="Navigation menu" tabindex="0">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
             style="width: 24px; height: 24px">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
      <!-- menu component -->
      <ul class="menu" role="list" tabindex="-1">
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}" class="menu-link">Bookmarks</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.archived' %}" class="menu-link">Archived bookmarks</a>
        </li>
        {% if request.user_profile.enable_sharing %}
          <li class="menu-item">
            <a href="{% url 'linkding:bookmarks.shared' %}" class="menu-link">Shared bookmarks</a>
          </li>
        {% endif %}
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}?unread=yes" class="menu-link">Unread</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:bookmarks.index' %}?q=!untagged" class="menu-link">Untagged</a>
        </li>
        <div class="divider"></div>
        <li class="menu-item">
          <a href="{% url 'linkding:settings.general' %}" class="menu-link">Settings</a>
        </li>
        <li class="menu-item">
          <a href="{% url 'linkding:settings.integrations' %}" class="menu-link">Integrations</a>
        </li>
        {% if request.user.is_superuser %}
          <li class="menu-item">
            <a href="{% url 'admin:index' %}" class="menu-link" data-turbo="false">Admin</a>
          </li>
        {% endif %}
        <div class="divider"></div>
        <li class="menu-item">
          <form class="d-inline" action="{% url 'logout' %}" method="post" data-turbo="false">
            {% csrf_token %}
            <button type="submit" class="btn btn-link menu-link">Logout</button>
          </form>
        </li>
      </ul>
    </div>
  </div>
{% endhtmlmin %}
