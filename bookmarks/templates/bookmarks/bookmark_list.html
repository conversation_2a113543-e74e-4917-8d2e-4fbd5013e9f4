{% load static %}
{% load shared %}
{% load pagination %}

{% if bookmark_list.is_empty %}
  {% include 'bookmarks/empty_bookmarks.html' %}
{% else %}
  <section aria-label="Bookmark list">
    <ul class="bookmark-list{% if bookmark_list.show_notes %} show-notes{% endif %}"
        role="list" tabindex="-1"
        style="--ld-bookmark-description-max-lines:{{ bookmark_list.description_max_lines }};"
        data-bookmarks-total="{{ bookmark_list.bookmarks_total }}">
      {% for bookmark_item in bookmark_list.items %}
        <li ld-bookmark-item data-bookmark-id="{{ bookmark_item.id }}" role="listitem"
            {% if bookmark_item.css_classes %}class="{{ bookmark_item.css_classes }}"{% endif %}>
          <div class="content">
            <div class="title">
              <label class="form-checkbox bulk-edit-checkbox">
                <input type="checkbox" name="bookmark_id" value="{{ bookmark_item.id }}">
                <i class="form-icon"></i>
              </label>
              {% if bookmark_item.favicon_file and bookmark_list.show_favicons %}
                <img class="favicon" src="{% static bookmark_item.favicon_file %}" alt="">
              {% endif %}
              <a href="{{ bookmark_item.url }}" target="{{ bookmark_list.link_target }}" rel="noopener">
                <span>{{ bookmark_item.title }}</span>
              </a>
            </div>
            {% if bookmark_list.show_url %}
              <div class="url-path truncate">
                <a href="{{ bookmark_item.url }}" target="{{ bookmark_list.link_target }}" rel="noopener"
                   class="url-display">
                  {{ bookmark_item.url }}
                </a>
              </div>
            {% endif %}
            {% if bookmark_list.description_display == 'inline' %}
              <div class="description inline truncate">
                {% if bookmark_item.tag_names %}
                  <span class="tags">
                  {% for tag_name in bookmark_item.tag_names %}
                    <a href="?{% add_tag_to_query tag_name %}">{{ tag_name|hash_tag }}</a>
                  {% endfor %}
                </span>
                {% endif %}
                {% if bookmark_item.tag_names and bookmark_item.description %} | {% endif %}
                {% if bookmark_item.description %}
                  <span>{{ bookmark_item.description }}</span>
                {% endif %}
              </div>
            {% else %}
              {% if bookmark_item.description %}
                <div class="description separate">{{ bookmark_item.description }}</div>
              {% endif %}
              {% if bookmark_item.tag_names %}
                <div class="tags">
                  {% for tag_name in bookmark_item.tag_names %}
                    <a href="?{% add_tag_to_query tag_name %}">{{ tag_name|hash_tag }}</a>
                  {% endfor %}
                </div>
              {% endif %}
            {% endif %}
            {% if bookmark_item.notes %}
              <div class="notes">
                <div class="markdown">{% markdown bookmark_item.notes %}</div>
              </div>
            {% endif %}
            <div class="actions">
              {% if bookmark_item.display_date %}
                {% if bookmark_item.snapshot_url %}
                  <a href="{{ bookmark_item.snapshot_url }}"
                     title="{{ bookmark_item.snapshot_title }}"
                     target="{{ bookmark_list.link_target }}"
                     rel="noopener">
                    {{ bookmark_item.display_date }}
                  </a>
                {% else %}
                  <span>{{ bookmark_item.display_date }}</span>
                {% endif %}
                {% if not bookmark_list.is_preview %}
                  <span>|</span>
                {% endif %}
              {% endif %}
              {% if not bookmark_list.is_preview %}
                {# View link is visible for both owned and shared bookmarks #}
                {% if bookmark_list.show_view_action %}
                  <a href="{{ bookmark_item.details_url }}" class="view-action"
                    data-turbo-action="replace" data-turbo-frame="details-modal">View</a>
                {% endif %}
                {% if bookmark_item.is_editable %}
                  {# Bookmark owner actions #}
                  {% if bookmark_list.show_edit_action %}
                    <a href="{% url 'linkding:bookmarks.edit' bookmark_item.id %}?return_url={{ bookmark_list.return_url|urlencode }}">Edit</a>
                  {% endif %}
                  {% if bookmark_list.show_archive_action %}
                    {% if bookmark_item.is_archived %}
                      <button type="submit" name="unarchive" value="{{ bookmark_item.id }}"
                              class="btn btn-link btn-sm">Unarchive
                      </button>
                    {% else %}
                      <button type="submit" name="archive" value="{{ bookmark_item.id }}"
                              class="btn btn-link btn-sm">Archive
                      </button>
                    {% endif %}
                  {% endif %}
                  {% if bookmark_list.show_remove_action %}
                    <button ld-confirm-button type="submit" name="remove" value="{{ bookmark_item.id }}"
                            class="btn btn-link btn-sm">Remove
                    </button>
                  {% endif %}
                {% else %}
                  {# Shared bookmark actions #}
                  <span>Shared by
                  <a href="?{% replace_query_param user=bookmark_item.owner.username %}">{{ bookmark_item.owner.username }}</a>
                </span>
                {% endif %}
                {% if bookmark_item.has_extra_actions %}
                  <div class="extra-actions">
                    <span class="hide-sm">|</span>
                    {% if bookmark_item.show_mark_as_read %}
                      <button type="submit" name="mark_as_read" value="{{ bookmark_item.id }}"
                              class="btn btn-link btn-sm btn-icon"
                              ld-confirm-button ld-confirm-icon="ld-icon-read" ld-confirm-question="Mark as read?">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16">
                          <use xlink:href="#ld-icon-unread"></use>
                        </svg>
                        Unread
                      </button>
                    {% endif %}
                    {% if bookmark_item.show_unshare %}
                      <button type="submit" name="unshare" value="{{ bookmark_item.id }}"
                              class="btn btn-link btn-sm btn-icon"
                              ld-confirm-button ld-confirm-icon="ld-icon-unshare" ld-confirm-question="Unshare?">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16">
                          <use xlink:href="#ld-icon-share"></use>
                        </svg>
                        Shared
                      </button>
                    {% endif %}
                    {% if bookmark_item.show_notes_button %}
                      <button type="button" class="btn btn-link btn-sm btn-icon toggle-notes">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16">
                          <use xlink:href="#ld-icon-note"></use>
                        </svg>
                        Notes
                      </button>
                    {% endif %}
                  </div>
                {% endif %}
              {% endif %}
            </div>
          </div>
          {% if bookmark_list.show_preview_images %}
            {% if bookmark_item.preview_image_file %}
              <img class="preview-image" src="{% static bookmark_item.preview_image_file %}" loading="lazy"/>
            {% else %}
              <div class="preview-image placeholder">
                <div class="img"/>
              </div>
            {% endif %}
          {% endif %}
        </li>
      {% endfor %}
    </ul>

    <div class="bookmark-pagination{% if request.user_profile.sticky_pagination %} sticky{% endif %}">
      {% pagination bookmark_list.bookmarks_page %}
    </div>
  </section>
{% endif %}
