{% load static %}
{% load shared %}

<form action="{{ details.action_url }}" method="post" enctype="multipart/form-data">
  {% csrf_token %}
  <input type="hidden" name="update_state" value="{{ details.bookmark.id }}">

  <div class="weblinks">
    <a class="weblink" href="{{ details.bookmark.url }}" rel="noopener"
       target="{{ details.profile.bookmark_link_target }}">
      {% if details.show_link_icons %}
        <img class="favicon" src="{% static details.bookmark.favicon_file %}" alt="">
      {% endif %}
      <span>{{ details.bookmark.url }}</span>
    </a>
    {% if details.latest_snapshot %}
      <a class="weblink" href="{% url 'linkding:assets.read' details.latest_snapshot.id %}"
         target="{{ details.profile.bookmark_link_target }}">
        {% if details.show_link_icons %}
          <svg class="favicon" xmlns="http://www.w3.org/2000/svg">
            <use xlink:href="#ld-icon-unread"></use>
          </svg>
        {% endif %}
        <span>Reader mode</span>
      </a>
    {% endif %}
    {% if details.web_archive_snapshot_url %}
      <a class="weblink" href="{{ details.web_archive_snapshot_url }}"
         target="{{ details.profile.bookmark_link_target }}">
        {% if details.show_link_icons %}
          <svg class="favicon" viewBox="0 0 76 86" xmlns="http://www.w3.org/2000/svg">
            <path
                d="m76 82v4h-76l.00080851-4zm-3-6v5h-70v-5zm-62.6696277-54 .8344146.4217275.4176066 6.7436084.4176065 10.9576581v10.5383496l-.4176065 13.1364492-.0694681 8.8498268-1.1825531.3523804h-4.17367003l-1.25202116-.3523804-.48627608-8.8498268-.41840503-13.0662957v-10.5375432l.41840503-11.028618.38167482-6.7798947.87034634-.3854412zm60.0004653 0 .8353798.4217275.4168913 6.7436084.4168913 10.9576581v10.5383496l-.4168913 13.1364492-.0686832 8.8498268-1.1835879.3523804h-4.1737047l-1.2522712-.3523804-.4879704-8.8498268-.4168913-13.0662957v-10.5375432l.4168913-11.028618.3833483-6.7798947.8697215-.3854412zm-42.000632 0 .8344979.4217275.4176483 6.7436084.4176482 10.9576581v10.5383496l-.4176482 13.1364492-.0686764 8.8498268-1.1834698.3523804h-4.1740866l-1.2529447-.3523804-.4863246-8.8498268-.4168497-13.0662957v-10.5375432l.4168497-11.028618.38331-6.7798947.8688361-.3854412zm23 0 .8344979.4217275.4176483 6.7436084.4176482 10.9576581v10.5383496l-.4176482 13.1364492-.0686764 8.8498268-1.1834698.3523804h-4.1740866l-1.2521462-.3523804-.4871231-8.8498268-.4168497-13.0662957v-10.5375432l.4168497-11.028618.38331-6.7798947.8696347-.3854412zm21.6697944-9v7h-70v-7zm-35.7200748-13 36.7200748 8.4088317-1.4720205 2.5911683h-70.32799254l-2.19998696-2.10140371z"
                fill="currentColor" fill-rule="evenodd"/>
          </svg>
        {% endif %}
        <span>Internet Archive</span>
      </a>
    {% endif %}
  </div>
  {% if details.preview_image_enabled and details.bookmark.preview_image_file %}
    <div class="preview-image">
      <img src="{% static details.bookmark.preview_image_file %}" alt=""/>
    </div>
  {% endif %}
  <div class="sections grid columns-2 columns-sm-1 gap-0">
    {% if details.is_editable %}
      <section class="status col-2">
        <h3>Status</h3>
        <div class="d-flex" style="gap: .8rem">
          <div class="form-group">
            <label class="form-switch">
              <input ld-auto-submit type="checkbox" name="is_archived"
                     {% if details.bookmark.is_archived %}checked{% endif %}>
              <i class="form-icon"></i> Archived
            </label>
          </div>
          <div class="form-group">
            <label class="form-switch">
              <input ld-auto-submit type="checkbox" name="unread"
                     {% if details.bookmark.unread %}checked{% endif %}>
              <i class="form-icon"></i> Unread
            </label>
          </div>
          {% if details.profile.enable_sharing %}
            <div class="form-group">
              <label class="form-switch">
                <input ld-auto-submit type="checkbox" name="shared"
                       {% if details.bookmark.shared %}checked{% endif %}>
                <i class="form-icon"></i> Shared
              </label>
            </div>
          {% endif %}
        </div>
      </section>
    {% endif %}
    <section class="files col-2">
      <h3>Files</h3>
      <div>
        {% include 'bookmarks/details/assets.html' %}
      </div>
    </section>
    {% if details.bookmark.tag_names %}
      <section class="tags col-1">
        <h3 id="details-modal-tags-title">Tags</h3>
        <div>
          {% for tag_name in details.bookmark.tag_names %}
            <a href="{% url 'linkding:bookmarks.index' %}?{% add_tag_to_query tag_name %}">{{ tag_name|hash_tag }}</a>
          {% endfor %}
        </div>
      </section>
    {% endif %}
    <section class="date-added col-1">
      <h3>Date added</h3>
      <div>
        <span>{{ details.bookmark.date_added }}</span>
      </div>
    </section>
    {% if details.bookmark.resolved_description %}
      <section class="description col-2">
        <h3>Description</h3>
        <div>{{ details.bookmark.resolved_description }}</div>
      </section>
    {% endif %}
    {% if details.bookmark.notes %}
      <section class="notes col-2">
        <h3>Notes</h3>
        <div class="markdown">{% markdown details.bookmark.notes %}</div>
      </section>
    {% endif %}
  </div>
</form>
