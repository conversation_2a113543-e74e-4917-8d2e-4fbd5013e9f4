<div class="modal active bookmark-details" ld-details-modal
     data-bookmark-id="{{ details.bookmark.id }}" data-close-url="{{ details.close_url }}">
  <div class="modal-overlay"></div>
  <div class="modal-container" role="dialog" aria-modal="true">
    <div class="modal-header">
      <h2 class="title">{{ details.bookmark.resolved_title }}</h2>
      <button class="close" aria-label="Close dialog">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
             stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
          <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
          <path d="M18 6l-12 12"></path>
          <path d="M6 6l12 12"></path>
        </svg>
      </button>
    </div>
    <div class="modal-body">
      <div class="content">
        {% include 'bookmarks/details/form.html' %}
      </div>
    </div>

    {% if details.is_editable %}
      <div class="modal-footer">
        <div class="actions">
          <div class="left-actions">
            <a class="btn btn-wide"
               href="{% url 'linkding:bookmarks.edit' details.bookmark.id %}?return_url={{ details.edit_return_url|urlencode }}">Edit</a>
          </div>
          <div class="right-actions">
            <form action="{{ details.delete_url }}" method="post" data-turbo-action="replace">
              {% csrf_token %}
              <input type="hidden" name="disable_turbo" value="true">
              <button ld-confirm-button class="btn btn-error btn-wide"
                      type="submit" name="remove" value="{{ details.bookmark.id }}">
                Delete...
              </button>
            </form>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>
