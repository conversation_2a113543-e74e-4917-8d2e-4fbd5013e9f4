{% extends 'bookmarks/layout.html' %}
{% load widget_tweaks %}

{% block head %}
  {% with page_title="Login - Linkding" %}
    {{ block.super }}
  {% endwith %}
{% endblock %}

{% block content %}
  <main class="mx-auto width-50 width-md-100" aria-labelledby="main-heading">
    <div class="section-header">
      <h1 id="main-heading">Login</h1>
    </div>
    <form method="post" action="{% url 'login' %}">
      {% csrf_token %}
      {% if form.errors %}
        <div class="form-group has-error">
          <p class="form-input-hint">Your username and password didn't match. Please try again.</p>
        </div>
      {% endif %}
      <div class="form-group">
        <label class="form-label" for="{{ form.username.id_for_label }}">Username</label>
        {{ form.username|add_class:'form-input'|attr:'placeholder: ' }}
      </div>
      <div class="form-group">
        <label class="form-label" for="{{ form.password.id_for_label }}">Password</label>
        {{ form.password|add_class:'form-input'|attr:'placeholder: ' }}
      </div>

      <br/>
      <div class="d-flex justify-between">
        <input type="submit" value="Login" class="btn btn-primary btn-wide"/>
        <input type="hidden" name="next" value="{{ next }}"/>
        {% if enable_oidc %}
          <a class="btn btn-link" href="{% url 'oidc_authentication_init' %}" data-turbo="false">Login with OIDC</a>
        {% endif %}
        {% if allow_registration %}
          <a href="{% url 'django_registration_register' %}" class="btn btn-link">Register</a>
        {% endif %}
      </div>
    </form>
  </main>
{% endblock %}
