{% extends 'bookmarks/layout.html' %}
{% load widget_tweaks %}

{% block head %}
  {% with page_title="Change password - Linkding" %}
    {{ block.super }}
  {% endwith %}
{% endblock %}

{% block content %}
  <main class="mx-auto width-50 width-md-100" aria-labelledby="main-heading">
    <div class="section-header">
      <h1 id="main-heading">Change Password</h1>
    </div>
    <form method="post" action="{% url 'change_password' %}">
      {% csrf_token %}
      <div class="form-group {% if form.old_password.errors %}has-error{% endif %}">
        <label class="form-label" for="{{ form.old_password.id_for_label }}">Old password</label>
        {{ form.old_password|add_class:'form-input'|attr:"placeholder: " }}
        {% if form.old_password.errors %}
          <div class="form-input-hint">
            {{ form.old_password.errors }}
          </div>
        {% endif %}
      </div>
      <div class="form-group {% if form.new_password1.errors %}has-error{% endif %}">
        <label class="form-label" for="{{ form.new_password1.id_for_label }}">New password</label>
        {{ form.new_password1|add_class:'form-input'|attr:"placeholder: " }}
        {% if form.new_password1.errors %}
          <div class="form-input-hint">
            {{ form.new_password1.errors }}
          </div>
        {% endif %}
      </div>
      <div class="form-group {% if form.new_password2.errors %}has-error{% endif %}">
        <label class="form-label" for="{{ form.new_password2.id_for_label }}">Confirm new password</label>
        {{ form.new_password2|add_class:'form-input'|attr:"placeholder: " }}
        {% if form.new_password2.errors %}
          <div class="form-input-hint">
            {{ form.new_password2.errors }}
          </div>
        {% endif %}
      </div>

      <br/>
      <input type="submit" value="Change Password" class="btn btn-primary btn-wide">
    </form>
  </main>
{% endblock %}
