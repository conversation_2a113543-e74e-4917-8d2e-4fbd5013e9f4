{% extends "bookmarks/layout.html" %}

{% block head %}
  {% with page_title="Integrations - Linkding" %}
    {{ block.super }}
  {% endwith %}
{% endblock %}

{% block content %}
  <main class="settings-page" aria-labelledby="main-heading">
    <h1 id="main-heading">Integrations</h1>

    <section aria-labelledby="browser-extension-heading">
      <h2 id="browser-extension-heading">Browser Extension</h2>
      <p>The browser extension allows you to quickly add new bookmarks without leaving the page that you are on. The
        extension is available in the official extension stores for:</p>
      <ul>
        <li><a href="https://addons.mozilla.org/firefox/addon/linkding-extension/" target="_blank">Firefox</a></li>
        <li><a href="https://chrome.google.com/webstore/detail/linkding-extension/beakmhbijpdhipnjhnclmhgjlddhidpe"
               target="_blank">Chrome</a></li>
      </ul>
      <p>The extension is <a href="https://github.com/sissbruecker/linkding-extension" target="_blank">open source</a>
        as well, which enables you to build and manually load it into any browser that supports Chrome extensions.</p>
      <h2>Bookmarklet</h2>
      <p>The bookmarklet is an alternative, cross-browser way to quickly add new bookmarks without opening the linkding
        application first. Here's how it works:</p>
      <ul>
        <li>Choose your preferred method for detecting website titles and descriptions below (<a href="https://linkding.link/troubleshooting/#automatically-detected-title-and-description-are-incorrect" target="_blank">Help</a>)</li>
        <li>Drag the bookmarklet below into your browser's bookmark bar / toolbar</li>
        <li>Open the website that you want to bookmark</li>
        <li>Click the bookmarklet in your browser's toolbar</li>
        <li>linkding opens in a new window or tab and allows you to add a bookmark for the site</li>
        <li>After saving the bookmark, the linkding window closes, and you are back on your website</li>
      </ul>

      <div class="form-group radio-group" role="radiogroup" aria-labelledby="detection-method-label">
        <p id="detection-method-label">Choose your preferred bookmarklet:</p>
        <label for="detection-method-server" class="form-radio">
          <input id="detection-method-server" type="radio" name="bookmarklet-type" value="server" checked>
          <i class="form-icon"></i>
          Detect title and description on the server
        </label>
        <label for="detection-method-client" class="form-radio">
          <input id="detection-method-client" type="radio" name="bookmarklet-type" value="client">
          <i class="form-icon"></i>
          Detect title and description in the browser
        </label>
      </div>
      <div class="bookmarklet-container">
        <a id="bookmarklet-server" href="javascript: {% include 'bookmarks/bookmarklet.js' %}" data-turbo="false"
           class="btn btn-primary">📎 Add bookmark</a>
        <a id="bookmarklet-client" href="javascript: {% include 'bookmarks/bookmarklet_clientside.js' %}" data-turbo="false"
           class="btn btn-primary" style="display: none;">📎 Add bookmark</a>
      </div>
    </section>

    <section aria-labelledby="rest-api-heading">
      <h2 id="rest-api-heading">REST API</h2>
      <p>The following token can be used to authenticate 3rd-party applications against the REST API:</p>
      <div class="form-group">
        <div class="width-50 width-md-100">
          <input class="form-input" value="{{ api_token }}" readonly>
        </div>
      </div>
      <p>
        <strong>Please treat this token as you would any other credential.</strong>
        Any party with access to this token can access and manage all your bookmarks.
        If you think that a token was compromised you can revoke (delete) it in the <a
          target="_blank" href="{% url 'admin:authtoken_tokenproxy_changelist' %}">admin panel</a>.
        After deleting the token, a new one will be generated when you reload this settings page.
      </p>
    </section>

    <section aria-labelledby="rss-feeds-heading">
      <h2 id="rss-feeds-heading">RSS Feeds</h2>
      <p>The following URLs provide RSS feeds for your bookmarks:</p>
      <ul style="list-style-position: outside;">
        <li><a target="_blank" href="{{ all_feed_url }}">All bookmarks</a></li>
        <li><a target="_blank" href="{{ unread_feed_url }}">Unread bookmarks</a></li>
        <li><a target="_blank" href="{{ shared_feed_url }}">Shared bookmarks</a></li>
        <li><a target="_blank" href="{{ public_shared_feed_url }}">Public shared bookmarks</a><br><span class="text-small text-secondary">The public shared feed does not contain an authentication token and can be shared with other people. Only shows shared bookmarks from users who have explicitly enabled public sharing.</span>
        </li>
      </ul>
      <p>
        All URLs support the following URL parameters:
      </p>
      <ul style="list-style-position: outside;">
        <li>A <code>limit</code> parameter for specifying the maximum number of bookmarks to include in the feed. By
          default, only the latest 100 matching bookmarks are included.
        </li>
        <li>A <code>q</code> URL parameter for specifying a search query. You can get an example by doing a search in
          the bookmarks view and then copying the parameter from the URL.
        </li>
        <li>An <code>unread</code> parameter for filtering for unread or read bookmarks. Use <code>yes</code> for unread
          bookmarks and <code>no</code> for read bookmarks.
        </li>
        <li>A <code>shared</code> parameter for filtering for shared or unshared bookmarks. Use <code>yes</code> for
          shared bookmarks and <code>no</code> for unshared bookmarks.
        </li>
      </ul>
      <p>
        <strong>Please note that these URLs include an authentication token that should be treated like any other
          credential.</strong>
        Any party with access to these URLs can read all your bookmarks.
        If you think that a URL was compromised you can delete the feed token for your user in the <a
          target="_blank" href="{% url 'admin:bookmarks_feedtoken_changelist' %}">admin panel</a>.
        After deleting the feed token, new URLs will be generated when you reload this settings page.
      </p>
    </section>
  </main>

  <script>
    (function init() {
      const radioButtons = document.querySelectorAll('input[name="bookmarklet-type"]');
      const serverBookmarklet = document.getElementById('bookmarklet-server');
      const clientBookmarklet = document.getElementById('bookmarklet-client');

      function toggleBookmarklet() {
        const selectedValue = document.querySelector('input[name="bookmarklet-type"]:checked').value;
        if (selectedValue === 'server') {
          serverBookmarklet.style.display = 'inline-block';
          clientBookmarklet.style.display = 'none';
        } else {
          serverBookmarklet.style.display = 'none';
          clientBookmarklet.style.display = 'inline-block';
        }
      }

      toggleBookmarklet();
      radioButtons.forEach(function(radio) {
        radio.addEventListener('change', toggleBookmarklet);
      });
    })();
  </script>
{% endblock %}
