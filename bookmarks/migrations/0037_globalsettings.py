# Generated by Django 5.0.8 on 2024-08-31 12:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bookmarks", "0036_userprofile_auto_tagging_rules"),
    ]

    operations = [
        migrations.CreateModel(
            name="GlobalSettings",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "landing_page",
                    models.CharField(
                        choices=[
                            ("login", "Login"),
                            ("shared_bookmarks", "Shared Bookmarks"),
                        ],
                        default="login",
                        max_length=50,
                    ),
                ),
            ],
        ),
    ]
