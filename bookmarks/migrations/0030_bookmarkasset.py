# Generated by Django 5.0.2 on 2024-03-31 08:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bookmarks", "0029_bookmark_list_actions_toast"),
    ]

    operations = [
        migrations.CreateModel(
            name="BookmarkAsset",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("file", models.CharField(blank=True, max_length=2048)),
                ("file_size", models.IntegerField(null=True)),
                ("asset_type", models.Char<PERSON>ield(max_length=64)),
                ("content_type", models.Char<PERSON>ield(max_length=128)),
                ("display_name", models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=2048)),
                ("status", models.<PERSON>r<PERSON><PERSON>(max_length=64)),
                ("gzip", models.<PERSON>olean<PERSON>ield(default=False)),
                (
                    "bookmark",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="bookmarks.bookmark",
                    ),
                ),
            ],
        ),
    ]
