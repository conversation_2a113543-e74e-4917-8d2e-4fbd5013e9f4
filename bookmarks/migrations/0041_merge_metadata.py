# Generated by Django 5.1.1 on 2024-09-21 08:13

from django.db import migrations
from django.db.models import Q
from django.db.models.expressions import RawSQL

from bookmarks.models import Bookmark


def forwards(apps, schema_editor):
    Bookmark.objects.filter(
        Q(title__isnull=True) | Q(title__exact=""),
    ).extra(
        where=["website_title IS NOT NULL"]
    ).update(title=RawSQL("website_title", ()))

    Bookmark.objects.filter(
        Q(description__isnull=True) | Q(description__exact=""),
    ).extra(where=["website_description IS NOT NULL"]).update(
        description=RawSQL("website_description", ())
    )


def reverse(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("bookmarks", "0040_userprofile_items_per_page_and_more"),
    ]

    operations = [
        migrations.RunPython(forwards, reverse),
    ]
