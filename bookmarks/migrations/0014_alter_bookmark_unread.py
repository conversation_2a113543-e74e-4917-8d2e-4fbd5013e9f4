# Generated by Django 3.2.13 on 2022-07-23 12:30

from django.db import migrations, models


def forwards(apps, schema_editor):
    Bookmark = apps.get_model("bookmarks", "Bookmark")
    Bookmark.objects.update(unread=False)


def reverse(apps, schema_editor):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ("bookmarks", "0013_web_archive_optin_toast"),
    ]

    operations = [
        migrations.AlterField(
            model_name="bookmark",
            name="unread",
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(forwards, reverse),
    ]
