# Generated by Django 5.1.9 on 2025-06-19 08:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bookmarks", "0044_bookmark_latest_snapshot"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="userprofile",
            name="hide_bundles",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="BookmarkBundle",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=256)),
                ("search", models.CharField(blank=True, max_length=256)),
                ("any_tags", models.CharField(blank=True, max_length=1024)),
                ("all_tags", models.Char<PERSON>ield(blank=True, max_length=1024)),
                ("excluded_tags", models.Char<PERSON>ield(blank=True, max_length=1024)),
                ("order", models.IntegerField(default=0)),
                ("date_created", models.DateTimeField(auto_now_add=True)),
                ("date_modified", models.DateTimeField(auto_now=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
