# Generated by Django 2.2.2 on 2019-06-28 23:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Bookmark",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("url", models.URLField()),
                ("title", models.CharField(max_length=512)),
                ("description", models.TextField()),
                (
                    "website_title",
                    models.CharField(blank=True, max_length=512, null=True),
                ),
                ("website_description", models.TextField(blank=True, null=True)),
                ("unread", models.BooleanField(default=True)),
                ("date_added", models.DateTimeField()),
                ("date_modified", models.DateTimeField()),
                ("date_accessed", models.DateTimeField(blank=True, null=True)),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
