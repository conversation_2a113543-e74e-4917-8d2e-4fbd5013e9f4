.show-sm,
.show-md {
  display: none !important;
}

.width-25 {
  width: 25%;
}

.width-50 {
  width: 50%;
}

.width-75 {
  width: 75%;
}

.width-100 {
  width: 100%;
}

.container {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: var(--size-lg);
}

.grid {
  --grid-columns: 3;
  display: grid;
  grid-template-columns: repeat(var(--grid-columns), 1fr);
  grid-gap: var(--unit-4);
}

.grid > * {
  min-width: 0;
}

.columns-2 {
  --grid-columns: 2;
}

.gap-0 {
  gap: 0;
}

.col-1 {
  grid-column: span min(1, var(--grid-columns));
}

.col-2 {
  grid-column: span min(2, var(--grid-columns));
}

.col-3 {
  grid-column: span min(3, var(--grid-columns));
}

@media (max-width: 840px) {
  .hide-md {
    display: none !important;
  }
  .show-md {
    display: block !important;
  }

  .width-md-25 {
    width: 25%;
  }
  .width-md-50 {
    width: 50%;
  }
  .width-md-75 {
    width: 75%;
  }
  .width-md-100 {
    width: 100%;
  }

  .columns-md-1 {
    --grid-columns: 1;
  }
  .columns-md-2 {
    --grid-columns: 2;
  }
}

@media (max-width: 600px) {
  .hide-sm {
    display: none !important;
  }
  .show-sm {
    display: block !important;
  }

  .width-sm-25 {
    width: 25%;
  }
  .width-sm-50 {
    width: 50%;
  }
  .width-sm-75 {
    width: 75%;
  }
  .width-sm-100 {
    width: 100%;
  }

  .columns-sm-1 {
    --grid-columns: 1;
  }
  .columns-sm-2 {
    --grid-columns: 2;
  }
}
