.markdown {
  & p,
  & ul,
  & ol,
  & pre,
  & blockquote {
    margin: 0 0 var(--unit-2) 0;
  }

  & > *:first-child {
    margin-top: 0;
  }

  & > *:last-child {
    margin-bottom: 0;
  }

  & ul,
  & ol {
    margin-left: var(--unit-4);
  }

  & ul li,
  & ol li {
    margin-top: var(--unit-1);
  }

  & pre {
    padding: var(--unit-1) var(--unit-2);
    background-color: var(--code-bg-color);
    border-radius: var(--unit-1);
    overflow-x: auto;
  }

  & pre code {
    background: none;
    box-shadow: none;
    padding: 0;
  }

  & > pre:first-child:last-child {
    padding: 0;
    background: none;
    border-radius: 0;
  }
}
