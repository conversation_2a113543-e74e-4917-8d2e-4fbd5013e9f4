[supervisord]
user=root
loglevel=info

[program:jobs]
user=www-data
# setup a temp home folder for the job, required by chromium
environment=HOME=/tmp/home
command=python manage.py run_huey -f
stdout_logfile=background_tasks.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
redirect_stderr=true

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock
