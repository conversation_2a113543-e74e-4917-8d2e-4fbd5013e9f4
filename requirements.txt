#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile
#
asgiref==3.8.1
    # via django
beautifulsoup4==4.12.3
    # via -r requirements.in
bleach==6.1.0
    # via -r requirements.in
bleach-allowlist==1.0.3
    # via -r requirements.in
certifi==2024.8.30
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.3.2
    # via requests
click==8.1.7
    # via waybackpy
confusable-homoglyphs==3.3.1
    # via django-registration
cryptography==43.0.1
    # via
    #   josepy
    #   mozilla-django-oidc
    #   pyopenssl
django==5.2.3
    # via
    #   -r requirements.in
    #   django-registration
    #   djangorestframework
    #   mozilla-django-oidc
django-registration==3.4
    # via -r requirements.in
django-widget-tweaks==1.5.0
    # via -r requirements.in
djangorestframework==3.15.2
    # via -r requirements.in
huey==2.5.1
    # via -r requirements.in
idna==3.10
    # via requests
josepy==1.14.0
    # via mozilla-django-oidc
markdown==3.7
    # via -r requirements.in
mozilla-django-oidc==4.0.1
    # via -r requirements.in
psycopg2-binary==2.9.9
    # via -r requirements.in
pycparser==2.22
    # via cffi
pyopenssl==24.2.1
    # via josepy
python-dateutil==2.9.0.post0
    # via -r requirements.in
requests==2.32.4
    # via
    #   -r requirements.in
    #   mozilla-django-oidc
    #   waybackpy
six==1.16.0
    # via
    #   bleach
    #   python-dateutil
soupsieve==2.6
    # via beautifulsoup4
sqlparse==0.5.1
    # via django
supervisor==4.2.5
    # via -r requirements.in
urllib3==2.5.0
    # via
    #   requests
    #   waybackpy
uwsgi==2.0.28
    # via -r requirements.in
waybackpy==3.0.6
    # via -r requirements.in
webencodings==0.5.1
    # via bleach

# The following packages are considered to be unsafe in a requirements file:
# setuptools
