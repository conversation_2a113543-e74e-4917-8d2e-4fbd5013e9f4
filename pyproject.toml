[project]
name = "linkding-import"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.18",
    "aiolinkding>=2025.2.0",
    "extruct>=0.18.0",
    "jinja2>=3.1.6",
    "lxml>=5.4.0",
    "metadata-parser>=0.12.3",
    "ollama>=0.4.9",
    "opengraph>=0.5",
    "playwright>=1.52.0",
    "pydantic>=2.11.4",
    "pydantic-settings>=2.9.1",
    "pyopengraph>=0.2",
    "requests>=2.32.3",
    "streamlit>=1.45.0",
    "typer>=0.15.3",
    "w3lib>=2.3.1",
    "watchdog>=6.0.0",
]

[dependency-groups]
dev = [
    "mypy>=1.17.1",
    "ruff>=0.11.8",
]
