[project]
name = "linkding"
version = "0.1.0"
requires-python = ">=3.12"
dependencies = [
    "asgiref==3.8.1",
    "beautifulsoup4==4.12.3",
    "bleach==6.1.0",
    "bleach-allowlist==1.0.3",
    "certifi==2024.8.30",
    "cffi==1.17.1",
    "charset-normalizer==3.3.2",
    "click==8.1.7",
    "confusable-homoglyphs==3.3.1",
    "cryptography==43.0.1",
    "django==5.2.3",
    "django-registration==3.4",
    "django-widget-tweaks==1.5.0",
    "djangorestframework==3.15.2",
    "huey==2.5.1",
    "idna==3.10",
    "josepy==1.14.0",
    "markdown==3.7",
    "mozilla-django-oidc==4.0.1",
    "psycopg2-binary==2.9.9",
    "pycparser==2.22",
    "pyopenssl==24.2.1",
    "python-dateutil==2.9.0.post0",
    "requests==2.32.4",
    "six==1.16.0",
    "soupsieve==2.6",
    "sqlparse==0.5.1",
    "supervisor==4.2.5",
    "urllib3==2.5.0",
    "uwsgi==2.0.28",
    "waybackpy==3.0.6",
    "webencodings==0.5.1",
]

[dependency-groups]
dev = [
    "asgiref==3.8.1",
    "black==24.8.0",
    "click==8.1.7",
    "coverage==7.6.1",
    "django==5.2.3",
    "django-debug-toolbar==5.2.0",
    "execnet==2.1.1",
    "greenlet==3.0.3",
    "iniconfig==2.0.0",
    "mypy>=1.17.1",
    "mypy-extensions==1.0.0",
    "packaging==24.1",
    "pathspec==0.12.1",
    "platformdirs==4.3.6",
    "playwright==1.47.0",
    "pluggy==1.5.0",
    "pyee==12.0.0",
    "pytest==8.3.3",
    "pytest-django==4.9.0",
    "pytest-xdist==3.6.1",
    "sqlparse==0.5.1",
    "typing-extensions==4.12.2",
]
