#
# This file is autogenerated by pip-compile with Python 3.12
# by the following command:
#
#    pip-compile requirements.dev.in
#
asgiref==3.8.1
    # via django
black==24.8.0
    # via -r requirements.dev.in
click==8.1.7
    # via black
coverage==7.6.1
    # via -r requirements.dev.in
django==5.2.3
    # via django-debug-toolbar
django-debug-toolbar==5.2.0
    # via -r requirements.dev.in
execnet==2.1.1
    # via pytest-xdist
greenlet==3.0.3
    # via playwright
iniconfig==2.0.0
    # via pytest
mypy-extensions==1.0.0
    # via black
packaging==24.1
    # via
    #   black
    #   pytest
pathspec==0.12.1
    # via black
platformdirs==4.3.6
    # via black
playwright==1.47.0
    # via -r requirements.dev.in
pluggy==1.5.0
    # via pytest
pyee==12.0.0
    # via playwright
pytest==8.3.3
    # via
    #   -r requirements.dev.in
    #   pytest-django
    #   pytest-xdist
pytest-django==4.9.0
    # via -r requirements.dev.in
pytest-xdist==3.6.1
    # via -r requirements.dev.in
sqlparse==0.5.1
    # via
    #   django
    #   django-debug-toolbar
typing-extensions==4.12.2
    # via pyee
