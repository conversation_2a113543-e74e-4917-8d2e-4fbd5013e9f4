{"categories": [{"id": "e260b423-db01-4743-a671-2cd38594c63c", "layoutType": "wide_grid", "name": "Shortcuts", "shortcuts": [{"bodyContent": "{{************************************}}", "codeOnPrepare": "const sharedValue = getVariable('text_and_url')\nconst matches = sharedValue.match(/\\bhttps?:\\/\\/\\S+/gi);\nconst url = matches[0];\nsetVariable('cleaned_url', url);", "contentType": "application/json", "description": "bookmark link", "headers": [{"id": "b66dd9b9-13e8-4802-b527-6e32f3980f4b", "key": "Authorization", "value": "Token {{908e3a30-ae82-400d-93c8-561c36d11d6d}}"}], "iconName": "flat_grey_pin", "id": "871c3219-9e9f-46bb-8a7f-78f1496f78fc", "method": "POST", "name": "<PERSON><PERSON>", "responseHandling": {"failureOutput": "simple", "uiType": "toast"}, "url": "{{26253fe2-d202-4ce8-acd1-55c1ad3ae7d1}}/api/bookmarks/"}]}], "variables": [{"id": "26253fe2-d202-4ce8-acd1-55c1ad3ae7d1", "key": "linkding_instance", "value": "https://your.linkding.host.no.slashed.end"}, {"id": "a3c8efa2-3e3a-4bb4-8919-3e831f95fe6a", "jsonEncode": true, "key": "linkding_tag", "message": "Comma separated", "title": "One or more tags", "type": "text"}, {"id": "908e3a30-ae82-400d-93c8-561c36d11d6d", "key": "linkding_api_key", "value": "your_api_key_here"}, {"id": "d76696e7-1ee1-4d98-b6f9-b570ec69ef40", "key": "cleaned_url"}, {"flags": 1, "id": "da66cdad-8118-4a87-9581-4db33852b610", "key": "text_and_url", "message": "Any text that contains one URL", "title": "URL", "type": "text"}, {"data": "{\"select\":{\"multi_select\":\"false\",\"separator\":\",\"}}", "id": "************************************", "key": "tag_yes_no_default", "options": [{"id": "9365e43e-0572-4621-ac06-caec1ccff09d", "label": "Tagged", "value": "{{5be61e61-d8f5-475b-b1b1-88ddaebf8fd5}}"}, {"id": "9f1caeaf-af57-42b4-8b10-4391354ad0f0", "label": "Untagged and unread", "value": "{{71ac9c4d-c03e-4b6f-ad75-9c112a591c50}}"}], "title": "Tagged or unread?", "type": "select"}, {"id": "5be61e61-d8f5-475b-b1b1-88ddaebf8fd5", "key": "request_body_tagged", "value": "{ \"url\": \"{{d76696e7-1ee1-4d98-b6f9-b570ec69ef40}}\", \"tag_names\": [ \"{{a3c8efa2-3e3a-4bb4-8919-3e831f95fe6a}}\" ] }"}, {"id": "71ac9c4d-c03e-4b6f-ad75-9c112a591c50", "key": "request_body_untagged", "value": "{ \"url\": \"{{d76696e7-1ee1-4d98-b6f9-b570ec69ef40}}\", \"unread\": true }"}], "version": 56}