{"$schema": "https://raw.githubusercontent.com/JetBrains/web-types/master/schema/web-types.json", "name": "linkding", "version": "1.0.0", "contributions": {"html": {"attributes": [{"name": "ld-fetch", "description": "Fetches the HTML content of the given URL and replaces the content of an element with it. Fires events afterwards to notify other behaviors.", "value": {"required": false}}, {"name": "ld-on", "description": "The event that triggers a fetch, such as `click` or a custom event name fired by another behavior", "value": {"required": false}}, {"name": "ld-target", "description": "The target element to replace the content of and the replacement strategy, for example `body|append`", "value": {"required": false}}, {"name": "ld-select", "description": "The content element(s) to select from the fetched content, for example `#main-content`", "value": {"required": false}}, {"name": "ld-interval", "description": "Automatically fetches the content of the given URL at the given interval, in seconds", "value": {"required": false}}, {"name": "ld-fire", "description": "Fires one or more events once a behavior, such as ld-fetch or ld-form, is finished", "value": {"required": false}}, {"name": "ld-form", "description": "Converts a form into a fetch request. Fires events afterwards to notify other behaviors.", "value": {"required": false}}, {"name": "ld-auto-submit", "description": "Automatically submits the nearest form when the value of the input changes", "value": {"required": false}}, {"name": "ld-upload-button", "description": "Opens the related file input when clicked, and submits the form when a file is selected", "value": {"required": false}}, {"name": "ld-modal", "description": "Adds Javascript behavior to a modal HTML component", "value": {"required": false}}, {"name": "ld-dropdown", "description": "Adds Javascript behavior to a dropdown HTML component", "value": {"required": false}}, {"name": "ld-confirm-button", "description": "Converts a button into a confirmation button that shows confirm / cancel buttons when clicked", "value": {"required": false}}, {"name": "ld-confirm-icon", "description": "Icon to show when the confirm button is clicked", "value": {"required": false}}, {"name": "ld-confirm-question", "description": "Question to show when the confirm button is clicked", "value": {"required": false}}, {"name": "ld-bookmark-item", "description": "Adds Javascript behavior to a bookmark list item", "value": {"required": false}}, {"name": "ld-bulk-edit", "description": "Adds Javascript behavior for bulk editing the bookmark list", "value": {"required": false}}, {"name": "ld-global-shortcuts", "description": "Adds Javascript behavior for global shortcuts", "value": {"required": false}}, {"name": "ld-tag-autocomplete", "description": "Adds Javascript behavior for converting a plain input into a tag autocomplete Svelte component", "value": {"required": false}}]}}}