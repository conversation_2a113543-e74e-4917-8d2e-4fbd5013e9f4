import typer
from typing_extensions import Annotated

from importer import service as importer_service
from tagger import service as tagger_service

app = typer.Typer()


@app.command()
def tag_bookmark(
    id: Annotated[int, typer.Argument()],
    model: Annotated[str, typer.Option()] = "gemma3:12b",
):
    tagger_service.tag_bookmark(id, model)


@app.command()
def import_bookmarks(file: Annotated[typer.FileText, typer.Argument()]):
    importer_service.import_bookmakrs(file)


if __name__ == "__main__":
    app()
